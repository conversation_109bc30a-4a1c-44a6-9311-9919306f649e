../lwip/src/core/tcp_in.c:118:1:tcp_input	64	static
../lwip/src/core/tcp_in.c:600:1:tcp_input_delayed_close	16	static
../lwip/src/core/tcp_in.c:630:1:tcp_listen_input	56	static
../lwip/src/core/tcp_in.c:739:1:tcp_timewait_input	32	static
../lwip/src/core/tcp_in.c:788:1:tcp_process	64	static
../lwip/src/core/tcp_in.c:1052:1:tcp_oos_insert_segment	32	static
../lwip/src/core/tcp_in.c:1089:1:tcp_free_acked_segments	40	static
../lwip/src/core/tcp_in.c:1142:1:tcp_receive	96	static
../lwip/src/core/tcp_in.c:1889:1:tcp_get_next_optbyte	16	static
../lwip/src/core/tcp_in.c:1910:1:tcp_parseopt	24	static
../lwip/src/core/tcp_in.c:2030:1:tcp_trigger_input_pcb_close	4	static
