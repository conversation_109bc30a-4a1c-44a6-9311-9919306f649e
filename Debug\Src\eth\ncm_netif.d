Src/eth/ncm_netif.o: ../Src/eth/ncm_netif.c ../Inc/eth/ncm_netif.h \
 ../lwip/src/include/lwip/netif.h ../lwip/src/include/lwip/opt.h \
 ../Inc/lwipopts.h ../lwip/src/include/lwip/debug.h \
 ../lwip/src/include/lwip/arch.h ../Inc/arch/cc.h \
 ../lwip/src/include/lwip/err.h ../lwip/src/include/lwip/ip_addr.h \
 ../lwip/src/include/lwip/def.h ../lwip/src/include/lwip/ip4_addr.h \
 ../lwip/src/include/lwip/ip6_addr.h ../lwip/src/include/lwip/def.h \
 ../lwip/src/include/lwip/pbuf.h ../lwip/src/include/lwip/stats.h \
 ../lwip/src/include/lwip/mem.h ../lwip/src/include/lwip/memp.h \
 ../lwip/src/include/lwip/priv/memp_std.h \
 ../lwip/src/include/lwip/priv/memp_priv.h \
 ../lwip/src/include/lwip/priv/mem_priv.h ../Inc/ncm_device.h \
 ../Inc/usb.h ../Inc/platform.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g484xx.h \
 ../Drivers/CMSIS/Include/core_cm4.h \
 ../Drivers/CMSIS/Include/cmsis_version.h \
 ../Drivers/CMSIS/Include/cmsis_compiler.h \
 ../Drivers/CMSIS/Include/cmsis_gcc.h \
 ../Drivers/CMSIS/Include/mpu_armv7.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h \
 ../Inc/profiling.h ../Inc/platform.h ../lwip/src/include/lwip/etharp.h \
 ../lwip/src/include/lwip/ip4.h ../lwip/src/include/lwip/prot/ip4.h \
 ../lwip/src/include/lwip/prot/ethernet.h \
 ../lwip/src/include/lwip/prot/ieee.h \
 ../lwip/src/include/lwip/prot/etharp.h \
 ../lwip/src/include/netif/ethernet.h
../Inc/eth/ncm_netif.h:
../lwip/src/include/lwip/netif.h:
../lwip/src/include/lwip/opt.h:
../Inc/lwipopts.h:
../lwip/src/include/lwip/debug.h:
../lwip/src/include/lwip/arch.h:
../Inc/arch/cc.h:
../lwip/src/include/lwip/err.h:
../lwip/src/include/lwip/ip_addr.h:
../lwip/src/include/lwip/def.h:
../lwip/src/include/lwip/ip4_addr.h:
../lwip/src/include/lwip/ip6_addr.h:
../lwip/src/include/lwip/def.h:
../lwip/src/include/lwip/pbuf.h:
../lwip/src/include/lwip/stats.h:
../lwip/src/include/lwip/mem.h:
../lwip/src/include/lwip/memp.h:
../lwip/src/include/lwip/priv/memp_std.h:
../lwip/src/include/lwip/priv/memp_priv.h:
../lwip/src/include/lwip/priv/mem_priv.h:
../Inc/ncm_device.h:
../Inc/usb.h:
../Inc/platform.h:
../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h:
../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g484xx.h:
../Drivers/CMSIS/Include/core_cm4.h:
../Drivers/CMSIS/Include/cmsis_version.h:
../Drivers/CMSIS/Include/cmsis_compiler.h:
../Drivers/CMSIS/Include/cmsis_gcc.h:
../Drivers/CMSIS/Include/mpu_armv7.h:
../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h:
../Inc/profiling.h:
../Inc/platform.h:
../lwip/src/include/lwip/etharp.h:
../lwip/src/include/lwip/ip4.h:
../lwip/src/include/lwip/prot/ip4.h:
../lwip/src/include/lwip/prot/ethernet.h:
../lwip/src/include/lwip/prot/ieee.h:
../lwip/src/include/lwip/prot/etharp.h:
../lwip/src/include/netif/ethernet.h:
