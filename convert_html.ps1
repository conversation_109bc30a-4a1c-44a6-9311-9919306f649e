# PowerShell script to convert HTML to hex array for fsdata.c

# Read the simple HTML file
$htmlContent = Get-Content -Path "simple_device.html" -Raw -Encoding UTF8

# Convert to bytes
$bytes = [System.Text.Encoding]::UTF8.GetBytes($htmlContent)

# Convert to hex array
$hexArray = ""
for ($i = 0; $i -lt $bytes.Length; $i++) {
    if ($i % 16 -eq 0) {
        $hexArray += "`n"
    }
    $hexArray += "0x{0:x2}," -f $bytes[$i]
}

# Remove trailing comma
$hexArray = $hexArray.TrimEnd(',')

Write-Host "HTML size: $($bytes.Length) bytes"
Write-Host "Hex array ready for fsdata.c"

# Read current fsdata.c
$fsdataContent = Get-Content -Path "Inc/eth/fs/fsdata.c" -Raw

# Replace the device.html section
$pattern = '(/\* raw file data \()(\d+)( bytes \*/\n)([^}]+)(};)'
$replacement = "`$1$($bytes.Length)`$3$hexArray`n`$5"

$newFsdataContent = $fsdataContent -replace $pattern, $replacement

# Write updated fsdata.c
$newFsdataContent | Set-Content -Path "Inc/eth/fs/fsdata.c" -Encoding UTF8

Write-Host "fsdata.c updated successfully!"
