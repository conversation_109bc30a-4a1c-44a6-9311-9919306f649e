# TimoTwo FX Module Control Implementation

## Fonctionnalité ajoutée

Cette implémentation ajoute le contrôle des modules TimoTwo FX via les pins PB12 et PB13 du STM32, permettant de configurer les modules en mode TX ou RX via leur Flex Mode.

## Mapping des pins

- **PB13** → Contrôle du module TimoTwo FX A (Port 1 / output0)
- **PB12** → Contrôle du module TimoTwo FX B (Port 2 / output1)

## Logique de contrôle

### Interface Web
- Quand le slider "Transmitter/Receiver" est sur **Receiver** (checkbox cochée) → `output0/output1 = 1`
- Quand le slider "Transmitter/Receiver" est sur **Transmitter** (checkbox non cochée) → `output0/output1 = 0`

### Contrôle des pins
- `output0 = 1` (Port 1 en Input/Receiver) → **PB13 = HIGH**
- `output0 = 0` (Port 1 en Output/Transmitter) → **PB13 = LOW**
- `output1 = 1` (Port 2 en Input/Receiver) → **PB12 = HIGH**
- `output1 = 0` (Port 2 en Output/Transmitter) → **PB12 = LOW**

## Fichiers modifiés

### 1. `Src/main.c`
- **Lignes 175-184** : Mise à jour des commentaires GPIO pour PB12 et PB13
- **Ligne 188** : Configuration de PB13 en GPIO Output (`GPIO_MODER_MODE13_0`)
- **Ligne 189** : Suppression du pull-down sur PB13 (`GPIO_PUPDR_PUPD13_1`)

### 2. `Src/config.c`
- **Ligne 11** : Ajout de `#include "stm32g4xx.h"`
- **Lignes 95-113** : Nouvelle fonction `Config_ApplyTimoTwoPins()`
- **Ligne 22** : Appel à `Config_ApplyTimoTwoPins()` dans `Config_Init()`
- **Ligne 126** : Appel à `Config_ApplyTimoTwoPins()` dans `Config_ApplyArtNet()`

### 3. `Inc/config.h`
- **Ligne 67** : Déclaration de `void Config_ApplyTimoTwoPins();`

## Comment tester

### Test 1 : Vérification de l'initialisation
1. Flasher le firmware modifié
2. Vérifier que les pins PB12 et PB13 sont configurées en sortie
3. Mesurer la tension sur PB12 et PB13 au démarrage (devrait correspondre à la configuration par défaut)

### Test 2 : Test via l'interface web
1. Se connecter à l'interface web du Steady Node
2. Aller sur la page "Port Config"
3. Pour le **CRMX A** :
   - Mettre le slider sur "Receiver" → PB13 devrait passer à HIGH (3.3V)
   - Mettre le slider sur "Transmitter" → PB13 devrait passer à LOW (0V)
4. Pour le **CRMX B** :
   - Mettre le slider sur "Receiver" → PB12 devrait passer à HIGH (3.3V)
   - Mettre le slider sur "Transmitter" → PB12 devrait passer à LOW (0V)
5. Cliquer sur "Submit" pour sauvegarder
6. Vérifier que les changements persistent après redémarrage

### Test 3 : Test avec modules TimoTwo FX
1. Connecter les modules TimoTwo FX aux pins PB12 et PB13
2. Configurer via l'interface web
3. Vérifier que les modules passent bien en mode TX/RX selon la configuration

## Points d'attention

- Les pins PB12 et PB13 sont maintenant dédiées au contrôle TimoTwo FX
- PB13 n'est plus utilisée pour la navigation OLED
- Les changements sont appliqués immédiatement lors de la soumission du formulaire web
- La configuration est sauvegardée en flash et restaurée au redémarrage

## Référence TimoTwo FX Flex Mode

Voir la documentation officielle : https://docs.lumenrad.io/timotwo/flex-mode/
