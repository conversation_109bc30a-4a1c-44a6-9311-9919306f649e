################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (12.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Src/config.c \
../Src/dmx.c \
../Src/dmx_usart.c \
../Src/flash_ee.c \
../Src/main.c \
../Src/ncm_device.c \
../Src/platform.c \
../Src/profiling.c \
../Src/system_stm32g4xx.c \
../Src/systimer.c \
../Src/usart.c \
../Src/usb.c \
../Src/usb_config.c 

OBJS += \
./Src/config.o \
./Src/dmx.o \
./Src/dmx_usart.o \
./Src/flash_ee.o \
./Src/main.o \
./Src/ncm_device.o \
./Src/platform.o \
./Src/profiling.o \
./Src/system_stm32g4xx.o \
./Src/systimer.o \
./Src/usart.o \
./Src/usb.o \
./Src/usb_config.o 

C_DEPS += \
./Src/config.d \
./Src/dmx.d \
./Src/dmx_usart.d \
./Src/flash_ee.d \
./Src/main.d \
./Src/ncm_device.d \
./Src/platform.d \
./Src/profiling.d \
./Src/system_stm32g4xx.d \
./Src/systimer.d \
./Src/usart.d \
./Src/usb.d \
./Src/usb_config.d 


# Each subdirectory must supply rules for building sources it contributes
Src/%.o Src/%.su Src/%.cyclo: ../Src/%.c Src/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DSTM32G484xx -c -I../Inc -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../lwip/src/include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-Src

clean-Src:
	-$(RM) ./Src/config.cyclo ./Src/config.d ./Src/config.o ./Src/config.su ./Src/dmx.cyclo ./Src/dmx.d ./Src/dmx.o ./Src/dmx.su ./Src/dmx_usart.cyclo ./Src/dmx_usart.d ./Src/dmx_usart.o ./Src/dmx_usart.su ./Src/flash_ee.cyclo ./Src/flash_ee.d ./Src/flash_ee.o ./Src/flash_ee.su ./Src/main.cyclo ./Src/main.d ./Src/main.o ./Src/main.su ./Src/ncm_device.cyclo ./Src/ncm_device.d ./Src/ncm_device.o ./Src/ncm_device.su ./Src/platform.cyclo ./Src/platform.d ./Src/platform.o ./Src/platform.su ./Src/profiling.cyclo ./Src/profiling.d ./Src/profiling.o ./Src/profiling.su ./Src/system_stm32g4xx.cyclo ./Src/system_stm32g4xx.d ./Src/system_stm32g4xx.o ./Src/system_stm32g4xx.su ./Src/systimer.cyclo ./Src/systimer.d ./Src/systimer.o ./Src/systimer.su ./Src/usart.cyclo ./Src/usart.d ./Src/usart.o ./Src/usart.su ./Src/usb.cyclo ./Src/usb.d ./Src/usb.o ./Src/usb.su ./Src/usb_config.cyclo ./Src/usb_config.d ./Src/usb_config.o ./Src/usb_config.su

.PHONY: clean-Src

