################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (12.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../lwip/src/apps/lwiperf/lwiperf.c 

OBJS += \
./lwip/src/apps/lwiperf/lwiperf.o 

C_DEPS += \
./lwip/src/apps/lwiperf/lwiperf.d 


# Each subdirectory must supply rules for building sources it contributes
lwip/src/apps/lwiperf/%.o lwip/src/apps/lwiperf/%.su lwip/src/apps/lwiperf/%.cyclo: ../lwip/src/apps/lwiperf/%.c lwip/src/apps/lwiperf/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DSTM32G484xx -c -I../Inc -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../lwip/src/include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-lwip-2f-src-2f-apps-2f-lwiperf

clean-lwip-2f-src-2f-apps-2f-lwiperf:
	-$(RM) ./lwip/src/apps/lwiperf/lwiperf.cyclo ./lwip/src/apps/lwiperf/lwiperf.d ./lwip/src/apps/lwiperf/lwiperf.o ./lwip/src/apps/lwiperf/lwiperf.su

.PHONY: clean-lwip-2f-src-2f-apps-2f-lwiperf

