################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (12.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Src/eth/artnet.c \
../Src/eth/dhcp_server.c \
../Src/eth/global.c \
../Src/eth/http_custom.c \
../Src/eth/ncm_netif.c 

OBJS += \
./Src/eth/artnet.o \
./Src/eth/dhcp_server.o \
./Src/eth/global.o \
./Src/eth/http_custom.o \
./Src/eth/ncm_netif.o 

C_DEPS += \
./Src/eth/artnet.d \
./Src/eth/dhcp_server.d \
./Src/eth/global.d \
./Src/eth/http_custom.d \
./Src/eth/ncm_netif.d 


# Each subdirectory must supply rules for building sources it contributes
Src/eth/%.o Src/eth/%.su Src/eth/%.cyclo: ../Src/eth/%.c Src/eth/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DSTM32G484xx -c -I../Inc -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../lwip/src/include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-Src-2f-eth

clean-Src-2f-eth:
	-$(RM) ./Src/eth/artnet.cyclo ./Src/eth/artnet.d ./Src/eth/artnet.o ./Src/eth/artnet.su ./Src/eth/dhcp_server.cyclo ./Src/eth/dhcp_server.d ./Src/eth/dhcp_server.o ./Src/eth/dhcp_server.su ./Src/eth/global.cyclo ./Src/eth/global.d ./Src/eth/global.o ./Src/eth/global.su ./Src/eth/http_custom.cyclo ./Src/eth/http_custom.d ./Src/eth/http_custom.o ./Src/eth/http_custom.su ./Src/eth/ncm_netif.cyclo ./Src/eth/ncm_netif.d ./Src/eth/ncm_netif.o ./Src/eth/ncm_netif.su

.PHONY: clean-Src-2f-eth

