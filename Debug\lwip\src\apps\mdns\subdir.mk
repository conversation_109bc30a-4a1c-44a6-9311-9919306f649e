################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (12.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../lwip/src/apps/mdns/mdns.c 

OBJS += \
./lwip/src/apps/mdns/mdns.o 

C_DEPS += \
./lwip/src/apps/mdns/mdns.d 


# Each subdirectory must supply rules for building sources it contributes
lwip/src/apps/mdns/%.o lwip/src/apps/mdns/%.su lwip/src/apps/mdns/%.cyclo: ../lwip/src/apps/mdns/%.c lwip/src/apps/mdns/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DSTM32G484xx -c -I../Inc -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../lwip/src/include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-lwip-2f-src-2f-apps-2f-mdns

clean-lwip-2f-src-2f-apps-2f-mdns:
	-$(RM) ./lwip/src/apps/mdns/mdns.cyclo ./lwip/src/apps/mdns/mdns.d ./lwip/src/apps/mdns/mdns.o ./lwip/src/apps/mdns/mdns.su

.PHONY: clean-lwip-2f-src-2f-apps-2f-mdns

