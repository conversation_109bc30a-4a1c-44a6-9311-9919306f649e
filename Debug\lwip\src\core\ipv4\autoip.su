../lwip/src/core/ipv4/autoip.c:106:1:autoip_set_struct	16	static
../lwip/src/core/ipv4/autoip.c:125:1:autoip_restart	24	static
../lwip/src/core/ipv4/autoip.c:136:1:autoip_handle_arp_conflict	24	static
../lwip/src/core/ipv4/autoip.c:169:1:autoip_create_addr	24	static
../lwip/src/core/ipv4/autoip.c:204:1:autoip_arp_probe	24	static
../lwip/src/core/ipv4/autoip.c:217:1:autoip_arp_announce	16	static
../lwip/src/core/ipv4/autoip.c:228:1:autoip_bind	32	static
../lwip/src/core/ipv4/autoip.c:255:1:autoip_start	24	static
../lwip/src/core/ipv4/autoip.c:299:1:autoip_start_probing	24	static
../lwip/src/core/ipv4/autoip.c:333:1:autoip_network_changed	24	static
../lwip/src/core/ipv4/autoip.c:349:1:autoip_stop	24	static
../lwip/src/core/ipv4/autoip.c:367:1:autoip_tmr	16	static
../lwip/src/core/ipv4/autoip.c:453:1:autoip_arp_reply	40	static
../lwip/src/core/ipv4/autoip.c:511:1:autoip_supplied_address	24	static
../lwip/src/core/ipv4/autoip.c:521:1:autoip_accept_packet	24	static
