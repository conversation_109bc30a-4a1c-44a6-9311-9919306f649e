Src/main.o: ../Src/main.c ../Inc/main.h ../Inc/platform.h \
 ../lwip/src/include/lwip/pbuf.h ../lwip/src/include/lwip/opt.h \
 ../Inc/lwipopts.h ../lwip/src/include/lwip/debug.h \
 ../lwip/src/include/lwip/arch.h ../Inc/arch/cc.h \
 ../lwip/src/include/lwip/err.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g484xx.h \
 ../Drivers/CMSIS/Include/core_cm4.h \
 ../Drivers/CMSIS/Include/cmsis_version.h \
 ../Drivers/CMSIS/Include/cmsis_compiler.h \
 ../Drivers/CMSIS/Include/cmsis_gcc.h \
 ../Drivers/CMSIS/Include/mpu_armv7.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h \
 ../Inc/config.h ../Inc/eth/artnet.h ../lwip/src/include/lwip/netif.h \
 ../lwip/src/include/lwip/ip_addr.h ../lwip/src/include/lwip/def.h \
 ../lwip/src/include/lwip/ip4_addr.h ../lwip/src/include/lwip/ip6_addr.h \
 ../lwip/src/include/lwip/def.h ../lwip/src/include/lwip/stats.h \
 ../lwip/src/include/lwip/mem.h ../lwip/src/include/lwip/memp.h \
 ../lwip/src/include/lwip/priv/memp_std.h \
 ../lwip/src/include/lwip/priv/memp_priv.h \
 ../lwip/src/include/lwip/priv/mem_priv.h ../lwip/src/include/lwip/udp.h \
 ../lwip/src/include/lwip/ip.h ../lwip/src/include/lwip/ip4.h \
 ../lwip/src/include/lwip/prot/ip4.h ../lwip/src/include/lwip/ip6.h \
 ../lwip/src/include/lwip/prot/ip.h ../lwip/src/include/lwip/prot/udp.h \
 ../Inc/eth/artnet.h ../Inc/eth/dhcp_server.h ../Inc/eth/http_custom.h \
 ../lwip/src/include/lwip/apps/fs.h \
 ../lwip/src/include/lwip/apps/httpd_opts.h \
 ../lwip/src/include/lwip/prot/iana.h ../Inc/systimer.h \
 ../Inc/eth/ncm_netif.h ../lwip/src/include/lwip/apps/httpd.h \
 ../lwip/src/include/lwip/apps/mdns.h \
 ../lwip/src/include/lwip/apps/mdns_opts.h \
 ../lwip/src/include/lwip/igmp.h ../lwip/src/include/lwip/init.h \
 ../lwip/src/include/lwip/timeouts.h ../Inc/ncm_device.h ../Inc/usb.h \
 ../Inc/profiling.h ../Inc/usart.h ../Inc/config.h ../Inc/usb.h
../Inc/main.h:
../Inc/platform.h:
../lwip/src/include/lwip/pbuf.h:
../lwip/src/include/lwip/opt.h:
../Inc/lwipopts.h:
../lwip/src/include/lwip/debug.h:
../lwip/src/include/lwip/arch.h:
../Inc/arch/cc.h:
../lwip/src/include/lwip/err.h:
../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h:
../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g484xx.h:
../Drivers/CMSIS/Include/core_cm4.h:
../Drivers/CMSIS/Include/cmsis_version.h:
../Drivers/CMSIS/Include/cmsis_compiler.h:
../Drivers/CMSIS/Include/cmsis_gcc.h:
../Drivers/CMSIS/Include/mpu_armv7.h:
../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h:
../Inc/config.h:
../Inc/eth/artnet.h:
../lwip/src/include/lwip/netif.h:
../lwip/src/include/lwip/ip_addr.h:
../lwip/src/include/lwip/def.h:
../lwip/src/include/lwip/ip4_addr.h:
../lwip/src/include/lwip/ip6_addr.h:
../lwip/src/include/lwip/def.h:
../lwip/src/include/lwip/stats.h:
../lwip/src/include/lwip/mem.h:
../lwip/src/include/lwip/memp.h:
../lwip/src/include/lwip/priv/memp_std.h:
../lwip/src/include/lwip/priv/memp_priv.h:
../lwip/src/include/lwip/priv/mem_priv.h:
../lwip/src/include/lwip/udp.h:
../lwip/src/include/lwip/ip.h:
../lwip/src/include/lwip/ip4.h:
../lwip/src/include/lwip/prot/ip4.h:
../lwip/src/include/lwip/ip6.h:
../lwip/src/include/lwip/prot/ip.h:
../lwip/src/include/lwip/prot/udp.h:
../Inc/eth/artnet.h:
../Inc/eth/dhcp_server.h:
../Inc/eth/http_custom.h:
../lwip/src/include/lwip/apps/fs.h:
../lwip/src/include/lwip/apps/httpd_opts.h:
../lwip/src/include/lwip/prot/iana.h:
../Inc/systimer.h:
../Inc/eth/ncm_netif.h:
../lwip/src/include/lwip/apps/httpd.h:
../lwip/src/include/lwip/apps/mdns.h:
../lwip/src/include/lwip/apps/mdns_opts.h:
../lwip/src/include/lwip/igmp.h:
../lwip/src/include/lwip/init.h:
../lwip/src/include/lwip/timeouts.h:
../Inc/ncm_device.h:
../Inc/usb.h:
../Inc/profiling.h:
../Inc/usart.h:
../Inc/config.h:
../Inc/usb.h:
