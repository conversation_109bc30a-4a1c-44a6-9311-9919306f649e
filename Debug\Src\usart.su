../Drivers/CMSIS/Include/core_cm4.h:1679:22:__NVIC_EnableIRQ	16	static,ignoring_inline_asm
../Drivers/CMSIS/Include/core_cm4.h:1809:22:__NVIC_SetPriority	16	static
../Src/usart.c:47:6:USART_Init	24	static
../Src/usart.c:60:6:USART_Tick	16	static
../Src/usart.c:68:6:USART_ChangeDirection	16	static
../Src/usart.c:87:6:USART_UpdateInputEnabled	16	static
../Src/usart.c:99:6:USART_Transmit	24	static
../Src/usart.c:110:6:USART_Receive	24	static
../Src/usart.c:113:13:USART_HandleIRQ	24	static
../Src/usart.c:182:13:USART_DisableDma	16	static
../Src/usart.c:187:13:USART_Disable	16	static
../Src/usart.c:198:13:USART_SetTx	16	static
../Src/usart.c:220:13:USART_SetRx	16	static
../Src/usart.c:244:13:USART_TxDma	24	static
../Src/usart.c:257:13:USART_RxDma	24	static
../Src/usart.c:265:13:USART_SendBreak	16	static
../Src/usart.c:278:6:USART1_IRQHandler	8	static
../Src/usart.c:282:6:USART2_IRQHandler	8	static
../Src/usart.c:286:6:USART3_IRQHandler	8	static
../Src/usart.c:290:6:UART4_IRQHandler	8	static
../Src/usart.c:294:6:DMA1_Channel1_IRQHandler	8	static
../Src/usart.c:298:6:DMA1_Channel2_IRQHandler	8	static
../Src/usart.c:302:6:DMA1_Channel3_IRQHandler	8	static
../Src/usart.c:306:6:DMA1_Channel4_IRQHanlder	8	static
