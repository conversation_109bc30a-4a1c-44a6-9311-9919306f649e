<title>Steady Node</title><link href=style.css rel=stylesheet><style>.loader{border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;width:32px;height:32px;animation:spin 2s linear infinite}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}</style><script>setTimeout((()=>{setInterval((async()=>{200==(await fetch("/")).status&&setTimeout((()=>{window.location.replace("/")}),1e3)}),1e3)}),2e3);</script><h1><i>Steady Node</i></h1><div><nav><a href=/ >Port Config</a> <a href=/device.html>Device Config</a></nav><div class="container vert"><h3>DFU Mode</h3><div>The device is currently in DFU mode. To upload a new Firmware, use the STM32CubeProgrammer.</div><div>To switch back into normal mode, unplug and replug the device.</div><div class=loader></div></div></div>