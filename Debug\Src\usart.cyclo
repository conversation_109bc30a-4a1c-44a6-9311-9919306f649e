../Drivers/CMSIS/Include/core_cm4.h:1679:22:__NVIC_EnableIRQ	2
../Drivers/CMSIS/Include/core_cm4.h:1809:22:__NVIC_SetPriority	2
../Src/usart.c:47:6:USART_Init	3
../Src/usart.c:60:6:<PERSON>RT_Tick	3
../Src/usart.c:68:6:USART_ChangeDirection	7
../Src/usart.c:87:6:USART_UpdateInputEnabled	4
../Src/usart.c:99:6:USART_Transmit	1
../Src/usart.c:110:6:USART_Receive	1
../Src/usart.c:113:13:USART_HandleIRQ	13
../Src/usart.c:182:13:USART_DisableDma	1
../Src/usart.c:187:13:USART_Disable	1
../Src/usart.c:198:13:USART_SetTx	2
../Src/usart.c:220:13:USART_SetRx	2
../Src/usart.c:244:13:USART_TxDma	1
../Src/usart.c:257:13:USART_RxDma	1
../Src/usart.c:265:13:USART_SendBreak	1
../Src/usart.c:278:6:USART1_IRQHandler	1
../Src/usart.c:282:6:USART2_IRQHandler	1
../Src/usart.c:286:6:USART3_IRQHandler	1
../Src/usart.c:290:6:UART4_IRQHandler	1
../Src/usart.c:294:6:DMA1_Channel1_IRQHandler	1
../Src/usart.c:298:6:DMA1_Channel2_IRQHandler	1
../Src/usart.c:302:6:DMA1_Channel3_IRQHandler	1
../Src/usart.c:306:6:DMA1_Channel4_IRQHanlder	1
