../lwip/src/apps/http/altcp_proxyconnect.c:79:1:altcp_proxyconnect_state_alloc	16	static
../lwip/src/apps/http/altcp_proxyconnect.c:86:1:altcp_proxyconnect_state_free	16	static
../lwip/src/apps/http/altcp_proxyconnect.c:103:1:altcp_proxyconnect_format_request	32	static
../lwip/src/apps/http/altcp_proxyconnect.c:110:1:altcp_proxyconnect_send_request	48	static
../lwip/src/apps/http/altcp_proxyconnect.c:161:1:altcp_proxyconnect_lower_connected	32	static
../lwip/src/apps/http/altcp_proxyconnect.c:187:1:altcp_proxyconnect_lower_recv	48	static
../lwip/src/apps/http/altcp_proxyconnect.c:251:1:altcp_proxyconnect_lower_sent	32	static
../lwip/src/apps/http/altcp_proxyconnect.c:276:1:altcp_proxyconnect_lower_poll	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:290:1:altcp_proxyconnect_lower_err	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:306:1:altcp_proxyconnect_setup_callbacks	16	static
../lwip/src/apps/http/altcp_proxyconnect.c:317:1:altcp_proxyconnect_setup	32	static
../lwip/src/apps/http/altcp_proxyconnect.c:346:1:altcp_proxyconnect_new	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:370:1:altcp_proxyconnect_new_tcp	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:397:1:altcp_proxyconnect_alloc	16	static
../lwip/src/apps/http/altcp_proxyconnect.c:434:1:altcp_proxyconnect_set_poll	16	static
../lwip/src/apps/http/altcp_proxyconnect.c:442:1:altcp_proxyconnect_recved	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:459:1:altcp_proxyconnect_connect	32	static
../lwip/src/apps/http/altcp_proxyconnect.c:484:1:altcp_proxyconnect_listen	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:494:1:altcp_proxyconnect_abort	16	static
../lwip/src/apps/http/altcp_proxyconnect.c:505:1:altcp_proxyconnect_close	24	static
../lwip/src/apps/http/altcp_proxyconnect.c:523:1:altcp_proxyconnect_write	32	static
../lwip/src/apps/http/altcp_proxyconnect.c:546:1:altcp_proxyconnect_dealloc	24	static
