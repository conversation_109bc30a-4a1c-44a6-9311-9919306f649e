# PA15 Control Implementation - CRMX B Power Control

## 🎯 Objective Completed

Successfully implemented PA15 control for CRMX B module power management using the existing `enable1` variable.

## 📋 Summary of Changes

### 1. **Interface Web Modifications**

#### File: `Inc/eth/fs/index.html`
- **Line 77-79**: Changed switch labels from "CRMX B Off" / "CRMX B On" to **"Power Off"** / **"Power On"**
- The switch remains linked to `enable1` with `value="1"` when activated
- Default state: Off (unchecked)

#### File: `Inc/eth/fs/min/index.html`
- **Line 1**: Updated minified HTML version with "Power Off" / "Power On" labels
- Replaced "Enable CRMX B" with the new format in the compressed HTML

#### File: `Inc/eth/fs/fsdata.c`
- **Lines 340-347**: Updated hexadecimal data to reflect the new HTML content
- Replaced "Enable CRMX B" with "Power Off" and "Power On" in the encoded data

### 2. **STM32 Firmware Modifications**

#### File: `Src/main.c`
- **Line 168**: Updated comment for PA15: "CRMX B Control (GPIO Output)"
- PA15 was already configured as GPIO Output Push-Pull (no hardware changes needed)

#### File: `Src/config.c`
- **Line 11**: Added `#include "stm32g4xx.h"` for GPIO register access
- **Lines 96-105**: New function `Config_ApplyCrmxBControl()` with English comments:
  ```c
  void Config_ApplyCrmxBControl() {
      // PA15 controls CRMX B module enable/disable
      // When enable1 == 1 -> PA15 = HIGH (3.3V)
      // When enable1 == 0 -> PA15 = LOW (GND)
      
      if (activeConfig.ArtNet[1].PortEnabled) {
          GPIOA->BSRR = GPIO_BSRR_BS15;  // Set PA15 HIGH
      } else {
          GPIOA->BSRR = GPIO_BSRR_BR15;  // Set PA15 LOW
      }
  }
  ```
- **Line 23**: Added call in `Config_Init()` for initialization
- **Line 132**: Added call in `Config_ApplyArtNet()` for configuration updates

#### File: `Inc/config.h`
- **Line 66**: Added function declaration: `void Config_ApplyCrmxBControl();`

## 🔧 How PA15 is Controlled

### Hardware Configuration
- **PA15** is configured as **GPIO Output Push-Pull** in `GPIO_Init()`
- Uses direct register access via `GPIOA->BSRR` for atomic pin control

### Control Logic
- **enable1 == 1** (Power On) → **PA15 = HIGH (3.3V)**
- **enable1 == 0** (Power Off) → **PA15 = LOW (GND)**

### Integration Points
1. **Initialization**: `Config_Init()` → `Config_ApplyCrmxBControl()`
2. **Web Updates**: Form submission → `httpc_parsePortConfig()` → `Config_ApplyArtNet()` → `Config_ApplyCrmxBControl()`
3. **Atomic Control**: Uses `GPIOA->BSRR` register for thread-safe pin manipulation

## 📍 Files Modified

1. **`Inc/eth/fs/index.html`** - Web interface labels
2. **`Inc/eth/fs/min/index.html`** - Minified web interface labels
3. **`Inc/eth/fs/fsdata.c`** - Encoded HTML data
4. **`Src/main.c`** - GPIO comments update
5. **`Src/config.c`** - PA15 control function and integration
6. **`Inc/config.h`** - Function declaration

## ✅ Verification

- All files compile without errors
- PA15 control function uses English comments only
- Web interface displays "Power Off" / "Power On" correctly
- Existing DMX logic and structure remain unchanged
- Variable `enable1` is reused without modifications to parsing logic

## 🚀 Ready for Testing

The implementation is complete and ready for hardware testing. PA15 will be controlled according to the web interface switch state, providing power control for the CRMX B module.
