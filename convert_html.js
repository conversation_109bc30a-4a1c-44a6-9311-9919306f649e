// Simple Node.js script to convert HTML to hex array for fsdata.c
const fs = require('fs');

function htmlToHexArray(htmlContent) {
    const bytes = Buffer.from(htmlContent, 'utf8');
    let hexArray = '';
    
    for (let i = 0; i < bytes.length; i++) {
        if (i % 16 === 0) {
            hexArray += '\n';
        }
        hexArray += `0x${bytes[i].toString(16).padStart(2, '0')},`;
    }
    
    // Remove trailing comma
    if (hexArray.endsWith(',')) {
        hexArray = hexArray.slice(0, -1);
    }
    
    return { hexArray, size: bytes.length };
}

try {
    // Read the minified HTML
    const htmlContent = fs.readFileSync('Inc/eth/fs/min/device.html', 'utf8');
    const { hexArray, size } = htmlToHexArray(htmlContent);
    
    console.log(`HTML size: ${size} bytes`);
    console.log('Hex array:');
    console.log(hexArray);
    
    // Read current fsdata.c
    const fsdataContent = fs.readFileSync('Inc/eth/fs/fsdata.c', 'utf8');
    
    // Replace the device.html section
    const pattern = /(\/\* raw file data \()(\d+)( bytes \*\/\n)([^}]+)(\};)/;
    const newFsdataContent = fsdataContent.replace(pattern, (match, p1, oldSize, p3, oldData, p5) => {
        console.log(`Replacing ${oldSize} bytes with ${size} bytes`);
        return `${p1}${size}${p3}${hexArray}\n${p5}`;
    });
    
    // Write updated fsdata.c
    fs.writeFileSync('Inc/eth/fs/fsdata.c', newFsdataContent);
    console.log('fsdata.c updated successfully!');
    
} catch (error) {
    console.error('Error:', error.message);
}
