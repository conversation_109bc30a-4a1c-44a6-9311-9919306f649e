#!/usr/bin/env python3
"""
<PERSON>ript to update fsdata.c with new HTML content
"""

def html_to_hex_array(html_content):
    """Convert HTML content to C hex array format"""
    hex_data = []
    for i, byte in enumerate(html_content.encode('utf-8')):
        if i % 16 == 0:
            hex_data.append('\n')
        hex_data.append(f'0x{byte:02x},')
    
    # Remove trailing comma and add final newline
    if hex_data and hex_data[-1].endswith(','):
        hex_data[-1] = hex_data[-1][:-1]
    
    return ''.join(hex_data)

def update_device_html_in_fsdata():
    """Update device.html data in fsdata.c"""
    
    # Read the new HTML content
    try:
        with open('Inc/eth/fs/device.html', 'r', encoding='utf-8') as f:
            new_html_content = f.read()
    except FileNotFoundError:
        print("Error: Inc/eth/fs/device.html not found")
        return False
    
    # Read current fsdata.c
    try:
        with open('Inc/eth/fs/fsdata.c', 'r', encoding='utf-8') as f:
            fsdata_content = f.read()
    except FileNotFoundError:
        print("Error: Inc/eth/fs/fsdata.c not found")
        return False
    
    # Convert HTML to hex array
    hex_array = html_to_hex_array(new_html_content)
    new_size = len(new_html_content.encode('utf-8'))
    
    # Find the device.html section and replace it
    import re
    
    # Pattern to match the device.html section
    pattern = r'(static const unsigned char data__device_html\[\] = \{[^}]+/device\.html[^}]+\n/\* raw file data \()(\d+)( bytes \)\*/\n)([^}]+)(\};)'
    
    def replacement(match):
        prefix = match.group(1)
        old_size = match.group(2)
        middle = match.group(3)
        old_data = match.group(4)
        suffix = match.group(5)
        
        print(f"Updating device.html: {old_size} bytes -> {new_size} bytes")
        
        return f"{prefix}{new_size}{middle}{hex_array}\n{suffix}"
    
    # Replace the content
    new_fsdata_content = re.sub(pattern, replacement, fsdata_content, flags=re.DOTALL)
    
    if new_fsdata_content == fsdata_content:
        print("Warning: No replacement made - pattern might not match")
        return False
    
    # Write updated fsdata.c
    try:
        with open('Inc/eth/fs/fsdata.c', 'w', encoding='utf-8') as f:
            f.write(new_fsdata_content)
        print("Successfully updated Inc/eth/fs/fsdata.c")
        return True
    except Exception as e:
        print(f"Error writing fsdata.c: {e}")
        return False

if __name__ == "__main__":
    success = update_device_html_in_fsdata()
    if success:
        print("fsdata.c updated successfully!")
    else:
        print("Failed to update fsdata.c")
