<html>

<head>
    <title>Steady Node</title>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <script>
        window.onload = async () => {
            for (elem of document.querySelectorAll("input[type='ip']")) {
                elem.type = "text";
                elem.minLength = 7;
                elem.maxLength = 15;
                elem.size = 15;
                elem.pattern = "((^|\\.)((25[0-5])|(2[0-4]\\d)|(1\\d\\d)|([1-9]?\\d))){4}$";
                elem.title = "IP Address";
                elem.required = true;
            }

            var result = await fetch("/ipconfig");
            var buf = await result.arrayBuffer();
            var decoder = new TextDecoder('iso-8859-1');
            var txt = decoder.decode(buf);
            var obj = txt.split('\n').reduce(function (result, item) { result[item.split(':')[0]] = item.split(':')[1]; return result; }, {});

            for (const [key, value] of Object.entries(obj)) {
                var elem = document.getElementsByName(key)[0];
                if (elem.type == 'checkbox') {
                    elem.checked = value == "1" ? 1 : 0;
                } else if (elem.type == 'radio') {
                    elem = document.querySelector(`input[name='${key}'][value='${value}']`);
                    elem.checked = 1;
                } else {
                    elem.value = value;
                }
            }
        }

        // Firmware upload functionality
        function setupFirmwareUpload() {
            const fileInput = document.getElementById('firmware-file');
            const uploadBtn = document.getElementById('upload-btn');

            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file && file.name.toLowerCase().endsWith('.efl')) {
                    uploadBtn.disabled = false;
                    document.getElementById('upload-status').textContent = '';
                } else {
                    uploadBtn.disabled = true;
                    document.getElementById('upload-status').textContent = 'Please select a valid .efl file';
                }
            });
        }

        async function uploadFirmware() {
            const fileInput = document.getElementById('firmware-file');
            const file = fileInput.files[0];

            if (!file) {
                document.getElementById('upload-status').textContent = 'No file selected';
                return;
            }

            if (!file.name.toLowerCase().endsWith('.efl')) {
                document.getElementById('upload-status').textContent = 'Invalid file type. Please select a .efl file';
                return;
            }

            const uploadBtn = document.getElementById('upload-btn');
            const progressDiv = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const statusDiv = document.getElementById('upload-status');

            uploadBtn.disabled = true;
            progressDiv.style.display = 'block';
            statusDiv.textContent = 'Uploading firmware...';
            statusDiv.style.color = 'blue';

            try {
                const formData = new FormData();
                formData.append('firmware', file);

                const xhr = new XMLHttpRequest();

                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.value = percentComplete;
                        progressText.textContent = Math.round(percentComplete) + '%';
                    }
                });

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        statusDiv.textContent = 'Firmware uploaded successfully! Device will reboot...';
                        statusDiv.style.color = 'green';
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    } else {
                        statusDiv.textContent = 'Upload failed: ' + xhr.responseText;
                        statusDiv.style.color = 'red';
                        uploadBtn.disabled = false;
                    }
                    progressDiv.style.display = 'none';
                };

                xhr.onerror = function() {
                    statusDiv.textContent = 'Upload failed: Network error';
                    statusDiv.style.color = 'red';
                    uploadBtn.disabled = false;
                    progressDiv.style.display = 'none';
                };

                xhr.open('POST', '/firmware-update', true);
                xhr.send(formData);

            } catch (error) {
                statusDiv.textContent = 'Upload failed: ' + error.message;
                statusDiv.style.color = 'red';
                uploadBtn.disabled = false;
                progressDiv.style.display = 'none';
            }
        }

        // Initialize firmware upload when page loads
        window.addEventListener('load', setupFirmwareUpload);
    </script>
</head>

<body>
    <h1><i>Steady Node</i></h1>

    <div>
        <nav>
            <a href="/">Port Config</a>
            <a href="/device.html">Device Config</a>
        </nav>

        <form class="container" action="/ip-config" method="post">
            <h3 style="grid-column: 1 / span 4">Device</h3>
            <div>
                <fieldset>
                    <legend>IP Mode</legend>

                    <label class="radio"><input type="radio" value="0" name="ipmode"> Auto IP</label>
                    <label class="radio"><input type="radio" value="1" name="ipmode"> Static</label>
                    <label class="radio"><input type="radio" value="2" name="ipmode"> DHCP</label>
                </fieldset>

                <fieldset>
                    <legend>Static Settings</legend>

                    <label>IP<input type="ip" name="s_ip"></label>
                    <label>Netmask<input type="ip" name="s_mask"></label>
                    <label>Gateway<input type="ip" name="s_gw"></label>
                </fieldset>

                <fieldset>
                    <legend>DHCP Server</legend>

                    <div class="auto-grid">
                        Enable DHCP Server
                        <label class="slider"><input type="checkbox" name="dhcp_en"><span></span></label>
                    </div>

                    <label>Device IP<input type="ip" name="d_dev"></label>
                    <label>Host IP<input type="ip" name="d_host"></label>
                    <label>Netmask<input type="ip" name="d_mask"></label>
                </fieldset>
            </div>

            <div>
                <fieldset>
                    <legend>Active IP Config</legend>

                    <label>mDNS Name<input type="text" value="artnet.local" readonly></label>
                    <label>IP<input type="text" name="a_ip" readonly></label>
                    <label>Netmask<input type="text" name="a_mask" readonly></label>
                    <label>Gateway<input type="text" name="a_gw" readonly></label>
                </fieldset>

                <fieldset>
                    <legend>Firmware</legend>
                    <label>Version<input type="text" name="f_v" readonly></label>
                    <label>ArtNET-ID<input type="text" name="f_id" readonly></label>
                </fieldset>

                <fieldset>
                    <legend>Firmware Update</legend>
                    <div style="margin-bottom: 10px;">
                        <label for="firmware-file">Select .efl file:</label>
                        <input type="file" id="firmware-file" accept=".efl" style="margin-top: 5px;">
                    </div>
                    <button type="button" id="upload-btn" onclick="uploadFirmware()" disabled>Upload Firmware</button>
                    <div id="upload-progress" style="margin-top: 10px; display: none;">
                        <div>Upload Progress:</div>
                        <progress id="progress-bar" value="0" max="100"></progress>
                        <span id="progress-text">0%</span>
                    </div>
                    <div id="upload-status" style="margin-top: 10px; color: red;"></div>
                </fieldset>

                <input id="submit" type="submit" value="Apply Settings">
                <div style="height: 40px"></div>


                <input type="submit" value="Reset Config" form="rstCfg">
                <input type="submit" value="Reboot Device" form="rstDev">
                <input type="submit" value="Reboot into DFU Mode" form="rstDfu">
            </div>
        </form>
    </div>

    <form action="/reset-config" method="post" id="rstCfg" hidden></form>
    <form action="/reset-device" method="post" id="rstDev" hidden></form>
    <form action="/reset-dfu" method="post" id="rstDfu" hidden></form>
</body>

</html>