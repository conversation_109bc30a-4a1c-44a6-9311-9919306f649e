../lwip/src/core/tcp.c:201:1:tcp_init	4	static
../lwip/src/core/tcp.c:210:1:tcp_free	16	static
../lwip/src/core/tcp.c:221:1:tcp_free_listen	16	static
../lwip/src/core/tcp.c:234:1:tcp_tmr	8	static
../lwip/src/core/tcp.c:251:1:tcp_remove_listener	24	static
../lwip/src/core/tcp.c:269:1:tcp_listen_closed	24	static
../lwip/src/core/tcp.c:294:1:tcp_backlog_delayed	16	static
../lwip/src/core/tcp.c:317:1:tcp_backlog_accepted	16	static
../lwip/src/core/tcp.c:348:1:tcp_close_shutdown	48	static
../lwip/src/core/tcp.c:409:1:tcp_close_shutdown_fin	24	static
../lwip/src/core/tcp.c:484:1:tcp_close	16	static
../lwip/src/core/tcp.c:515:1:tcp_shutdown	24	static
../lwip/src/core/tcp.c:563:1:tcp_abandon	64	static
../lwip/src/core/tcp.c:638:1:tcp_abort	16	static
../lwip/src/core/tcp.c:662:1:tcp_bind	40	static
../lwip/src/core/tcp.c:764:1:tcp_bind_netif	16	static
../lwip/src/core/tcp.c:779:1:tcp_accept_null	24	static
../lwip/src/core/tcp.c:826:1:tcp_listen_with_backlog	16	static
../lwip/src/core/tcp.c:849:1:tcp_listen_with_backlog_and_err	40	static
../lwip/src/core/tcp.c:931:1:tcp_update_rcv_ann_wnd	24	static
../lwip/src/core/tcp.c:969:1:tcp_recved	24	static
../lwip/src/core/tcp.c:1012:1:tcp_new_port	16	static
../lwip/src/core/tcp.c:1068:1:tcp_connect	48	static
../lwip/src/core/tcp.c:1193:1:tcp_slowtmr	88	static
../lwip/src/core/tcp.c:1480:1:tcp_fasttmr	16	static
../lwip/src/core/tcp.c:1527:1:tcp_txnow	16	static
../lwip/src/core/tcp.c:1540:1:tcp_process_refused_data	40	static
../lwip/src/core/tcp.c:1609:1:tcp_segs_free	24	static
../lwip/src/core/tcp.c:1624:1:tcp_seg_free	16	static
../lwip/src/core/tcp.c:1645:1:tcp_setprio	16	static
../lwip/src/core/tcp.c:1663:1:tcp_seg_copy	24	static
../lwip/src/core/tcp.c:1685:1:tcp_recv_null	24	static
../lwip/src/core/tcp.c:1707:1:tcp_kill_prio	32	static
../lwip/src/core/tcp.c:1753:1:tcp_kill_state	32	static
../lwip/src/core/tcp.c:1785:1:tcp_kill_timewait	24	static
../lwip/src/core/tcp.c:1812:1:tcp_handle_closepend	16	static
../lwip/src/core/tcp.c:1835:1:tcp_alloc	24	static
../lwip/src/core/tcp.c:1947:1:tcp_new	8	static
../lwip/src/core/tcp.c:1965:1:tcp_new_ip_type	24	static
../lwip/src/core/tcp.c:1991:1:tcp_arg	16	static
../lwip/src/core/tcp.c:2014:1:tcp_recv	16	static
../lwip/src/core/tcp.c:2034:1:tcp_sent	16	static
../lwip/src/core/tcp.c:2060:1:tcp_err	16	static
../lwip/src/core/tcp.c:2080:1:tcp_accept	24	static
../lwip/src/core/tcp.c:2109:1:tcp_poll	24	static
../lwip/src/core/tcp.c:2131:1:tcp_pcb_purge	16	static
../lwip/src/core/tcp.c:2181:1:tcp_pcb_remove	24	static
../lwip/src/core/tcp.c:2219:1:tcp_next_iss	16	static
../lwip/src/core/tcp.c:2242:1:tcp_eff_send_mss_netif	32	static
../lwip/src/core/tcp.c:2303:1:tcp_netif_ip_addr_changed_pcblist	24	static
../lwip/src/core/tcp.c:2335:1:tcp_netif_ip_addr_changed	24	static
../lwip/src/core/tcp.c:2358:1:tcp_debug_state_str	16	static
../lwip/src/core/tcp.c:2364:1:tcp_tcp_get_tcp_addrinfo	24	static
../lwip/src/core/tcp.c:2390:1:tcp_free_ooseq	16	static
